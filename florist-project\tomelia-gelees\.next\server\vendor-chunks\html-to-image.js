"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-to-image";
exports.ids = ["vendor-chunks/html-to-image"];
exports.modules = {

/***/ "(ssr)/./node_modules/html-to-image/es/apply-style.js":
/*!******************************************************!*\
  !*** ./node_modules/html-to-image/es/apply-style.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyStyle: () => (/* binding */ applyStyle)\n/* harmony export */ });\nfunction applyStyle(node, options) {\n    const { style } = node;\n    if (options.backgroundColor) {\n        style.backgroundColor = options.backgroundColor;\n    }\n    if (options.width) {\n        style.width = `${options.width}px`;\n    }\n    if (options.height) {\n        style.height = `${options.height}px`;\n    }\n    const manual = options.style;\n    if (manual != null) {\n        Object.keys(manual).forEach((key) => {\n            style[key] = manual[key];\n        });\n    }\n    return node;\n}\n//# sourceMappingURL=apply-style.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbC10by1pbWFnZS9lcy9hcHBseS1zdHlsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCxZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsY0FBYztBQUN2QztBQUNBO0FBQ0EsMEJBQTBCLGVBQWU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxpbmdhcFxcT25lRHJpdmVcXFN0YWxpbmlzIGtvbXBpdXRlcmlzXFxSZWFjdC1Ccmlja3NcXGZsb3Jpc3QtcHJvamVjdFxcdG9tZWxpYS1nZWxlZXNcXG5vZGVfbW9kdWxlc1xcaHRtbC10by1pbWFnZVxcZXNcXGFwcGx5LXN0eWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBhcHBseVN0eWxlKG5vZGUsIG9wdGlvbnMpIHtcbiAgICBjb25zdCB7IHN0eWxlIH0gPSBub2RlO1xuICAgIGlmIChvcHRpb25zLmJhY2tncm91bmRDb2xvcikge1xuICAgICAgICBzdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSBvcHRpb25zLmJhY2tncm91bmRDb2xvcjtcbiAgICB9XG4gICAgaWYgKG9wdGlvbnMud2lkdGgpIHtcbiAgICAgICAgc3R5bGUud2lkdGggPSBgJHtvcHRpb25zLndpZHRofXB4YDtcbiAgICB9XG4gICAgaWYgKG9wdGlvbnMuaGVpZ2h0KSB7XG4gICAgICAgIHN0eWxlLmhlaWdodCA9IGAke29wdGlvbnMuaGVpZ2h0fXB4YDtcbiAgICB9XG4gICAgY29uc3QgbWFudWFsID0gb3B0aW9ucy5zdHlsZTtcbiAgICBpZiAobWFudWFsICE9IG51bGwpIHtcbiAgICAgICAgT2JqZWN0LmtleXMobWFudWFsKS5mb3JFYWNoKChrZXkpID0+IHtcbiAgICAgICAgICAgIHN0eWxlW2tleV0gPSBtYW51YWxba2V5XTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiBub2RlO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwbHktc3R5bGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/apply-style.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/clone-node.js":
/*!*****************************************************!*\
  !*** ./node_modules/html-to-image/es/clone-node.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cloneNode: () => (/* binding */ cloneNode)\n/* harmony export */ });\n/* harmony import */ var _clone_pseudos__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./clone-pseudos */ \"(ssr)/./node_modules/html-to-image/es/clone-pseudos.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/html-to-image/es/util.js\");\n/* harmony import */ var _mimes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mimes */ \"(ssr)/./node_modules/html-to-image/es/mimes.js\");\n/* harmony import */ var _dataurl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./dataurl */ \"(ssr)/./node_modules/html-to-image/es/dataurl.js\");\n\n\n\n\nasync function cloneCanvasElement(canvas) {\n    const dataURL = canvas.toDataURL();\n    if (dataURL === 'data:,') {\n        return canvas.cloneNode(false);\n    }\n    return (0,_util__WEBPACK_IMPORTED_MODULE_1__.createImage)(dataURL);\n}\nasync function cloneVideoElement(video, options) {\n    if (video.currentSrc) {\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        canvas.width = video.clientWidth;\n        canvas.height = video.clientHeight;\n        ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n        const dataURL = canvas.toDataURL();\n        return (0,_util__WEBPACK_IMPORTED_MODULE_1__.createImage)(dataURL);\n    }\n    const poster = video.poster;\n    const contentType = (0,_mimes__WEBPACK_IMPORTED_MODULE_2__.getMimeType)(poster);\n    const dataURL = await (0,_dataurl__WEBPACK_IMPORTED_MODULE_3__.resourceToDataURL)(poster, contentType, options);\n    return (0,_util__WEBPACK_IMPORTED_MODULE_1__.createImage)(dataURL);\n}\nasync function cloneIFrameElement(iframe, options) {\n    var _a;\n    try {\n        if ((_a = iframe === null || iframe === void 0 ? void 0 : iframe.contentDocument) === null || _a === void 0 ? void 0 : _a.body) {\n            return (await cloneNode(iframe.contentDocument.body, options, true));\n        }\n    }\n    catch (_b) {\n        // Failed to clone iframe\n    }\n    return iframe.cloneNode(false);\n}\nasync function cloneSingleNode(node, options) {\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(node, HTMLCanvasElement)) {\n        return cloneCanvasElement(node);\n    }\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(node, HTMLVideoElement)) {\n        return cloneVideoElement(node, options);\n    }\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(node, HTMLIFrameElement)) {\n        return cloneIFrameElement(node, options);\n    }\n    return node.cloneNode(isSVGElement(node));\n}\nconst isSlotElement = (node) => node.tagName != null && node.tagName.toUpperCase() === 'SLOT';\nconst isSVGElement = (node) => node.tagName != null && node.tagName.toUpperCase() === 'SVG';\nasync function cloneChildren(nativeNode, clonedNode, options) {\n    var _a, _b;\n    if (isSVGElement(clonedNode)) {\n        return clonedNode;\n    }\n    let children = [];\n    if (isSlotElement(nativeNode) && nativeNode.assignedNodes) {\n        children = (0,_util__WEBPACK_IMPORTED_MODULE_1__.toArray)(nativeNode.assignedNodes());\n    }\n    else if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(nativeNode, HTMLIFrameElement) &&\n        ((_a = nativeNode.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n        children = (0,_util__WEBPACK_IMPORTED_MODULE_1__.toArray)(nativeNode.contentDocument.body.childNodes);\n    }\n    else {\n        children = (0,_util__WEBPACK_IMPORTED_MODULE_1__.toArray)(((_b = nativeNode.shadowRoot) !== null && _b !== void 0 ? _b : nativeNode).childNodes);\n    }\n    if (children.length === 0 ||\n        (0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(nativeNode, HTMLVideoElement)) {\n        return clonedNode;\n    }\n    await children.reduce((deferred, child) => deferred\n        .then(() => cloneNode(child, options))\n        .then((clonedChild) => {\n        if (clonedChild) {\n            clonedNode.appendChild(clonedChild);\n        }\n    }), Promise.resolve());\n    return clonedNode;\n}\nfunction cloneCSSStyle(nativeNode, clonedNode, options) {\n    const targetStyle = clonedNode.style;\n    if (!targetStyle) {\n        return;\n    }\n    const sourceStyle = window.getComputedStyle(nativeNode);\n    if (sourceStyle.cssText) {\n        targetStyle.cssText = sourceStyle.cssText;\n        targetStyle.transformOrigin = sourceStyle.transformOrigin;\n    }\n    else {\n        (0,_util__WEBPACK_IMPORTED_MODULE_1__.getStyleProperties)(options).forEach((name) => {\n            let value = sourceStyle.getPropertyValue(name);\n            if (name === 'font-size' && value.endsWith('px')) {\n                const reducedFont = Math.floor(parseFloat(value.substring(0, value.length - 2))) - 0.1;\n                value = `${reducedFont}px`;\n            }\n            if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(nativeNode, HTMLIFrameElement) &&\n                name === 'display' &&\n                value === 'inline') {\n                value = 'block';\n            }\n            if (name === 'd' && clonedNode.getAttribute('d')) {\n                value = `path(${clonedNode.getAttribute('d')})`;\n            }\n            targetStyle.setProperty(name, value, sourceStyle.getPropertyPriority(name));\n        });\n    }\n}\nfunction cloneInputValue(nativeNode, clonedNode) {\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(nativeNode, HTMLTextAreaElement)) {\n        clonedNode.innerHTML = nativeNode.value;\n    }\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(nativeNode, HTMLInputElement)) {\n        clonedNode.setAttribute('value', nativeNode.value);\n    }\n}\nfunction cloneSelectValue(nativeNode, clonedNode) {\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(nativeNode, HTMLSelectElement)) {\n        const clonedSelect = clonedNode;\n        const selectedOption = Array.from(clonedSelect.children).find((child) => nativeNode.value === child.getAttribute('value'));\n        if (selectedOption) {\n            selectedOption.setAttribute('selected', '');\n        }\n    }\n}\nfunction decorate(nativeNode, clonedNode, options) {\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(clonedNode, Element)) {\n        cloneCSSStyle(nativeNode, clonedNode, options);\n        (0,_clone_pseudos__WEBPACK_IMPORTED_MODULE_0__.clonePseudoElements)(nativeNode, clonedNode, options);\n        cloneInputValue(nativeNode, clonedNode);\n        cloneSelectValue(nativeNode, clonedNode);\n    }\n    return clonedNode;\n}\nasync function ensureSVGSymbols(clone, options) {\n    const uses = clone.querySelectorAll ? clone.querySelectorAll('use') : [];\n    if (uses.length === 0) {\n        return clone;\n    }\n    const processedDefs = {};\n    for (let i = 0; i < uses.length; i++) {\n        const use = uses[i];\n        const id = use.getAttribute('xlink:href');\n        if (id) {\n            const exist = clone.querySelector(id);\n            const definition = document.querySelector(id);\n            if (!exist && definition && !processedDefs[id]) {\n                // eslint-disable-next-line no-await-in-loop\n                processedDefs[id] = (await cloneNode(definition, options, true));\n            }\n        }\n    }\n    const nodes = Object.values(processedDefs);\n    if (nodes.length) {\n        const ns = 'http://www.w3.org/1999/xhtml';\n        const svg = document.createElementNS(ns, 'svg');\n        svg.setAttribute('xmlns', ns);\n        svg.style.position = 'absolute';\n        svg.style.width = '0';\n        svg.style.height = '0';\n        svg.style.overflow = 'hidden';\n        svg.style.display = 'none';\n        const defs = document.createElementNS(ns, 'defs');\n        svg.appendChild(defs);\n        for (let i = 0; i < nodes.length; i++) {\n            defs.appendChild(nodes[i]);\n        }\n        clone.appendChild(svg);\n    }\n    return clone;\n}\nasync function cloneNode(node, options, isRoot) {\n    if (!isRoot && options.filter && !options.filter(node)) {\n        return null;\n    }\n    return Promise.resolve(node)\n        .then((clonedNode) => cloneSingleNode(clonedNode, options))\n        .then((clonedNode) => cloneChildren(node, clonedNode, options))\n        .then((clonedNode) => decorate(node, clonedNode, options))\n        .then((clonedNode) => ensureSVGSymbols(clonedNode, options));\n}\n//# sourceMappingURL=clone-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/clone-node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/clone-pseudos.js":
/*!********************************************************!*\
  !*** ./node_modules/html-to-image/es/clone-pseudos.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clonePseudoElements: () => (/* binding */ clonePseudoElements)\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/html-to-image/es/util.js\");\n\nfunction formatCSSText(style) {\n    const content = style.getPropertyValue('content');\n    return `${style.cssText} content: '${content.replace(/'|\"/g, '')}';`;\n}\nfunction formatCSSProperties(style, options) {\n    return (0,_util__WEBPACK_IMPORTED_MODULE_0__.getStyleProperties)(options)\n        .map((name) => {\n        const value = style.getPropertyValue(name);\n        const priority = style.getPropertyPriority(name);\n        return `${name}: ${value}${priority ? ' !important' : ''};`;\n    })\n        .join(' ');\n}\nfunction getPseudoElementStyle(className, pseudo, style, options) {\n    const selector = `.${className}:${pseudo}`;\n    const cssText = style.cssText\n        ? formatCSSText(style)\n        : formatCSSProperties(style, options);\n    return document.createTextNode(`${selector}{${cssText}}`);\n}\nfunction clonePseudoElement(nativeNode, clonedNode, pseudo, options) {\n    const style = window.getComputedStyle(nativeNode, pseudo);\n    const content = style.getPropertyValue('content');\n    if (content === '' || content === 'none') {\n        return;\n    }\n    const className = (0,_util__WEBPACK_IMPORTED_MODULE_0__.uuid)();\n    try {\n        clonedNode.className = `${clonedNode.className} ${className}`;\n    }\n    catch (err) {\n        return;\n    }\n    const styleElement = document.createElement('style');\n    styleElement.appendChild(getPseudoElementStyle(className, pseudo, style, options));\n    clonedNode.appendChild(styleElement);\n}\nfunction clonePseudoElements(nativeNode, clonedNode, options) {\n    clonePseudoElement(nativeNode, clonedNode, ':before', options);\n    clonePseudoElement(nativeNode, clonedNode, ':after', options);\n}\n//# sourceMappingURL=clone-pseudos.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/clone-pseudos.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/dataurl.js":
/*!**************************************************!*\
  !*** ./node_modules/html-to-image/es/dataurl.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAsDataURL: () => (/* binding */ fetchAsDataURL),\n/* harmony export */   isDataUrl: () => (/* binding */ isDataUrl),\n/* harmony export */   makeDataUrl: () => (/* binding */ makeDataUrl),\n/* harmony export */   resourceToDataURL: () => (/* binding */ resourceToDataURL)\n/* harmony export */ });\nfunction getContentFromDataUrl(dataURL) {\n    return dataURL.split(/,/)[1];\n}\nfunction isDataUrl(url) {\n    return url.search(/^(data:)/) !== -1;\n}\nfunction makeDataUrl(content, mimeType) {\n    return `data:${mimeType};base64,${content}`;\n}\nasync function fetchAsDataURL(url, init, process) {\n    const res = await fetch(url, init);\n    if (res.status === 404) {\n        throw new Error(`Resource \"${res.url}\" not found`);\n    }\n    const blob = await res.blob();\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onerror = reject;\n        reader.onloadend = () => {\n            try {\n                resolve(process({ res, result: reader.result }));\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n        reader.readAsDataURL(blob);\n    });\n}\nconst cache = {};\nfunction getCacheKey(url, contentType, includeQueryParams) {\n    let key = url.replace(/\\?.*/, '');\n    if (includeQueryParams) {\n        key = url;\n    }\n    // font resource\n    if (/ttf|otf|eot|woff2?/i.test(key)) {\n        key = key.replace(/.*\\//, '');\n    }\n    return contentType ? `[${contentType}]${key}` : key;\n}\nasync function resourceToDataURL(resourceUrl, contentType, options) {\n    const cacheKey = getCacheKey(resourceUrl, contentType, options.includeQueryParams);\n    if (cache[cacheKey] != null) {\n        return cache[cacheKey];\n    }\n    // ref: https://developer.mozilla.org/en/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest#Bypassing_the_cache\n    if (options.cacheBust) {\n        // eslint-disable-next-line no-param-reassign\n        resourceUrl += (/\\?/.test(resourceUrl) ? '&' : '?') + new Date().getTime();\n    }\n    let dataURL;\n    try {\n        const content = await fetchAsDataURL(resourceUrl, options.fetchRequestInit, ({ res, result }) => {\n            if (!contentType) {\n                // eslint-disable-next-line no-param-reassign\n                contentType = res.headers.get('Content-Type') || '';\n            }\n            return getContentFromDataUrl(result);\n        });\n        dataURL = makeDataUrl(content, contentType);\n    }\n    catch (error) {\n        dataURL = options.imagePlaceholder || '';\n        let msg = `Failed to fetch resource: ${resourceUrl}`;\n        if (error) {\n            msg = typeof error === 'string' ? error : error.message;\n        }\n        if (msg) {\n            console.warn(msg);\n        }\n    }\n    cache[cacheKey] = dataURL;\n    return dataURL;\n}\n//# sourceMappingURL=dataurl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/dataurl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/embed-images.js":
/*!*******************************************************!*\
  !*** ./node_modules/html-to-image/es/embed-images.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedImages: () => (/* binding */ embedImages)\n/* harmony export */ });\n/* harmony import */ var _embed_resources__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./embed-resources */ \"(ssr)/./node_modules/html-to-image/es/embed-resources.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/html-to-image/es/util.js\");\n/* harmony import */ var _dataurl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dataurl */ \"(ssr)/./node_modules/html-to-image/es/dataurl.js\");\n/* harmony import */ var _mimes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mimes */ \"(ssr)/./node_modules/html-to-image/es/mimes.js\");\n\n\n\n\nasync function embedProp(propName, node, options) {\n    var _a;\n    const propValue = (_a = node.style) === null || _a === void 0 ? void 0 : _a.getPropertyValue(propName);\n    if (propValue) {\n        const cssString = await (0,_embed_resources__WEBPACK_IMPORTED_MODULE_0__.embedResources)(propValue, null, options);\n        node.style.setProperty(propName, cssString, node.style.getPropertyPriority(propName));\n        return true;\n    }\n    return false;\n}\nasync function embedBackground(clonedNode, options) {\n    ;\n    (await embedProp('background', clonedNode, options)) ||\n        (await embedProp('background-image', clonedNode, options));\n    (await embedProp('mask', clonedNode, options)) ||\n        (await embedProp('-webkit-mask', clonedNode, options)) ||\n        (await embedProp('mask-image', clonedNode, options)) ||\n        (await embedProp('-webkit-mask-image', clonedNode, options));\n}\nasync function embedImageNode(clonedNode, options) {\n    const isImageElement = (0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(clonedNode, HTMLImageElement);\n    if (!(isImageElement && !(0,_dataurl__WEBPACK_IMPORTED_MODULE_2__.isDataUrl)(clonedNode.src)) &&\n        !((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(clonedNode, SVGImageElement) &&\n            !(0,_dataurl__WEBPACK_IMPORTED_MODULE_2__.isDataUrl)(clonedNode.href.baseVal))) {\n        return;\n    }\n    const url = isImageElement ? clonedNode.src : clonedNode.href.baseVal;\n    const dataURL = await (0,_dataurl__WEBPACK_IMPORTED_MODULE_2__.resourceToDataURL)(url, (0,_mimes__WEBPACK_IMPORTED_MODULE_3__.getMimeType)(url), options);\n    await new Promise((resolve, reject) => {\n        clonedNode.onload = resolve;\n        clonedNode.onerror = options.onImageErrorHandler\n            ? (...attributes) => {\n                try {\n                    resolve(options.onImageErrorHandler(...attributes));\n                }\n                catch (error) {\n                    reject(error);\n                }\n            }\n            : reject;\n        const image = clonedNode;\n        if (image.decode) {\n            image.decode = resolve;\n        }\n        if (image.loading === 'lazy') {\n            image.loading = 'eager';\n        }\n        if (isImageElement) {\n            clonedNode.srcset = '';\n            clonedNode.src = dataURL;\n        }\n        else {\n            clonedNode.href.baseVal = dataURL;\n        }\n    });\n}\nasync function embedChildren(clonedNode, options) {\n    const children = (0,_util__WEBPACK_IMPORTED_MODULE_1__.toArray)(clonedNode.childNodes);\n    const deferreds = children.map((child) => embedImages(child, options));\n    await Promise.all(deferreds).then(() => clonedNode);\n}\nasync function embedImages(clonedNode, options) {\n    if ((0,_util__WEBPACK_IMPORTED_MODULE_1__.isInstanceOfElement)(clonedNode, Element)) {\n        await embedBackground(clonedNode, options);\n        await embedImageNode(clonedNode, options);\n        await embedChildren(clonedNode, options);\n    }\n}\n//# sourceMappingURL=embed-images.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/embed-images.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/embed-resources.js":
/*!**********************************************************!*\
  !*** ./node_modules/html-to-image/es/embed-resources.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embed: () => (/* binding */ embed),\n/* harmony export */   embedResources: () => (/* binding */ embedResources),\n/* harmony export */   parseURLs: () => (/* binding */ parseURLs),\n/* harmony export */   shouldEmbed: () => (/* binding */ shouldEmbed)\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/html-to-image/es/util.js\");\n/* harmony import */ var _mimes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mimes */ \"(ssr)/./node_modules/html-to-image/es/mimes.js\");\n/* harmony import */ var _dataurl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dataurl */ \"(ssr)/./node_modules/html-to-image/es/dataurl.js\");\n\n\n\nconst URL_REGEX = /url\\((['\"]?)([^'\"]+?)\\1\\)/g;\nconst URL_WITH_FORMAT_REGEX = /url\\([^)]+\\)\\s*format\\(([\"']?)([^\"']+)\\1\\)/g;\nconst FONT_SRC_REGEX = /src:\\s*(?:url\\([^)]+\\)\\s*format\\([^)]+\\)[,;]\\s*)+/g;\nfunction toRegex(url) {\n    // eslint-disable-next-line no-useless-escape\n    const escaped = url.replace(/([.*+?^${}()|\\[\\]\\/\\\\])/g, '\\\\$1');\n    return new RegExp(`(url\\\\(['\"]?)(${escaped})(['\"]?\\\\))`, 'g');\n}\nfunction parseURLs(cssText) {\n    const urls = [];\n    cssText.replace(URL_REGEX, (raw, quotation, url) => {\n        urls.push(url);\n        return raw;\n    });\n    return urls.filter((url) => !(0,_dataurl__WEBPACK_IMPORTED_MODULE_2__.isDataUrl)(url));\n}\nasync function embed(cssText, resourceURL, baseURL, options, getContentFromUrl) {\n    try {\n        const resolvedURL = baseURL ? (0,_util__WEBPACK_IMPORTED_MODULE_0__.resolveUrl)(resourceURL, baseURL) : resourceURL;\n        const contentType = (0,_mimes__WEBPACK_IMPORTED_MODULE_1__.getMimeType)(resourceURL);\n        let dataURL;\n        if (getContentFromUrl) {\n            const content = await getContentFromUrl(resolvedURL);\n            dataURL = (0,_dataurl__WEBPACK_IMPORTED_MODULE_2__.makeDataUrl)(content, contentType);\n        }\n        else {\n            dataURL = await (0,_dataurl__WEBPACK_IMPORTED_MODULE_2__.resourceToDataURL)(resolvedURL, contentType, options);\n        }\n        return cssText.replace(toRegex(resourceURL), `$1${dataURL}$3`);\n    }\n    catch (error) {\n        // pass\n    }\n    return cssText;\n}\nfunction filterPreferredFontFormat(str, { preferredFontFormat }) {\n    return !preferredFontFormat\n        ? str\n        : str.replace(FONT_SRC_REGEX, (match) => {\n            // eslint-disable-next-line no-constant-condition\n            while (true) {\n                const [src, , format] = URL_WITH_FORMAT_REGEX.exec(match) || [];\n                if (!format) {\n                    return '';\n                }\n                if (format === preferredFontFormat) {\n                    return `src: ${src};`;\n                }\n            }\n        });\n}\nfunction shouldEmbed(url) {\n    return url.search(URL_REGEX) !== -1;\n}\nasync function embedResources(cssText, baseUrl, options) {\n    if (!shouldEmbed(cssText)) {\n        return cssText;\n    }\n    const filteredCSSText = filterPreferredFontFormat(cssText, options);\n    const urls = parseURLs(filteredCSSText);\n    return urls.reduce((deferred, url) => deferred.then((css) => embed(css, url, baseUrl, options)), Promise.resolve(filteredCSSText));\n}\n//# sourceMappingURL=embed-resources.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/embed-resources.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/embed-webfonts.js":
/*!*********************************************************!*\
  !*** ./node_modules/html-to-image/es/embed-webfonts.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   embedWebFonts: () => (/* binding */ embedWebFonts),\n/* harmony export */   getWebFontCSS: () => (/* binding */ getWebFontCSS)\n/* harmony export */ });\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/html-to-image/es/util.js\");\n/* harmony import */ var _dataurl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dataurl */ \"(ssr)/./node_modules/html-to-image/es/dataurl.js\");\n/* harmony import */ var _embed_resources__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./embed-resources */ \"(ssr)/./node_modules/html-to-image/es/embed-resources.js\");\n\n\n\nconst cssFetchCache = {};\nasync function fetchCSS(url) {\n    let cache = cssFetchCache[url];\n    if (cache != null) {\n        return cache;\n    }\n    const res = await fetch(url);\n    const cssText = await res.text();\n    cache = { url, cssText };\n    cssFetchCache[url] = cache;\n    return cache;\n}\nasync function embedFonts(data, options) {\n    let cssText = data.cssText;\n    const regexUrl = /url\\([\"']?([^\"')]+)[\"']?\\)/g;\n    const fontLocs = cssText.match(/url\\([^)]+\\)/g) || [];\n    const loadFonts = fontLocs.map(async (loc) => {\n        let url = loc.replace(regexUrl, '$1');\n        if (!url.startsWith('https://')) {\n            url = new URL(url, data.url).href;\n        }\n        return (0,_dataurl__WEBPACK_IMPORTED_MODULE_1__.fetchAsDataURL)(url, options.fetchRequestInit, ({ result }) => {\n            cssText = cssText.replace(loc, `url(${result})`);\n            return [loc, result];\n        });\n    });\n    return Promise.all(loadFonts).then(() => cssText);\n}\nfunction parseCSS(source) {\n    if (source == null) {\n        return [];\n    }\n    const result = [];\n    const commentsRegex = /(\\/\\*[\\s\\S]*?\\*\\/)/gi;\n    // strip out comments\n    let cssText = source.replace(commentsRegex, '');\n    // eslint-disable-next-line prefer-regex-literals\n    const keyframesRegex = new RegExp('((@.*?keyframes [\\\\s\\\\S]*?){([\\\\s\\\\S]*?}\\\\s*?)})', 'gi');\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n        const matches = keyframesRegex.exec(cssText);\n        if (matches === null) {\n            break;\n        }\n        result.push(matches[0]);\n    }\n    cssText = cssText.replace(keyframesRegex, '');\n    const importRegex = /@import[\\s\\S]*?url\\([^)]*\\)[\\s\\S]*?;/gi;\n    // to match css & media queries together\n    const combinedCSSRegex = '((\\\\s*?(?:\\\\/\\\\*[\\\\s\\\\S]*?\\\\*\\\\/)?\\\\s*?@media[\\\\s\\\\S]' +\n        '*?){([\\\\s\\\\S]*?)}\\\\s*?})|(([\\\\s\\\\S]*?){([\\\\s\\\\S]*?)})';\n    // unified regex\n    const unifiedRegex = new RegExp(combinedCSSRegex, 'gi');\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n        let matches = importRegex.exec(cssText);\n        if (matches === null) {\n            matches = unifiedRegex.exec(cssText);\n            if (matches === null) {\n                break;\n            }\n            else {\n                importRegex.lastIndex = unifiedRegex.lastIndex;\n            }\n        }\n        else {\n            unifiedRegex.lastIndex = importRegex.lastIndex;\n        }\n        result.push(matches[0]);\n    }\n    return result;\n}\nasync function getCSSRules(styleSheets, options) {\n    const ret = [];\n    const deferreds = [];\n    // First loop inlines imports\n    styleSheets.forEach((sheet) => {\n        if ('cssRules' in sheet) {\n            try {\n                (0,_util__WEBPACK_IMPORTED_MODULE_0__.toArray)(sheet.cssRules || []).forEach((item, index) => {\n                    if (item.type === CSSRule.IMPORT_RULE) {\n                        let importIndex = index + 1;\n                        const url = item.href;\n                        const deferred = fetchCSS(url)\n                            .then((metadata) => embedFonts(metadata, options))\n                            .then((cssText) => parseCSS(cssText).forEach((rule) => {\n                            try {\n                                sheet.insertRule(rule, rule.startsWith('@import')\n                                    ? (importIndex += 1)\n                                    : sheet.cssRules.length);\n                            }\n                            catch (error) {\n                                console.error('Error inserting rule from remote css', {\n                                    rule,\n                                    error,\n                                });\n                            }\n                        }))\n                            .catch((e) => {\n                            console.error('Error loading remote css', e.toString());\n                        });\n                        deferreds.push(deferred);\n                    }\n                });\n            }\n            catch (e) {\n                const inline = styleSheets.find((a) => a.href == null) || document.styleSheets[0];\n                if (sheet.href != null) {\n                    deferreds.push(fetchCSS(sheet.href)\n                        .then((metadata) => embedFonts(metadata, options))\n                        .then((cssText) => parseCSS(cssText).forEach((rule) => {\n                        inline.insertRule(rule, inline.cssRules.length);\n                    }))\n                        .catch((err) => {\n                        console.error('Error loading remote stylesheet', err);\n                    }));\n                }\n                console.error('Error inlining remote css file', e);\n            }\n        }\n    });\n    return Promise.all(deferreds).then(() => {\n        // Second loop parses rules\n        styleSheets.forEach((sheet) => {\n            if ('cssRules' in sheet) {\n                try {\n                    (0,_util__WEBPACK_IMPORTED_MODULE_0__.toArray)(sheet.cssRules || []).forEach((item) => {\n                        ret.push(item);\n                    });\n                }\n                catch (e) {\n                    console.error(`Error while reading CSS rules from ${sheet.href}`, e);\n                }\n            }\n        });\n        return ret;\n    });\n}\nfunction getWebFontRules(cssRules) {\n    return cssRules\n        .filter((rule) => rule.type === CSSRule.FONT_FACE_RULE)\n        .filter((rule) => (0,_embed_resources__WEBPACK_IMPORTED_MODULE_2__.shouldEmbed)(rule.style.getPropertyValue('src')));\n}\nasync function parseWebFontRules(node, options) {\n    if (node.ownerDocument == null) {\n        throw new Error('Provided element is not within a Document');\n    }\n    const styleSheets = (0,_util__WEBPACK_IMPORTED_MODULE_0__.toArray)(node.ownerDocument.styleSheets);\n    const cssRules = await getCSSRules(styleSheets, options);\n    return getWebFontRules(cssRules);\n}\nfunction normalizeFontFamily(font) {\n    return font.trim().replace(/[\"']/g, '');\n}\nfunction getUsedFonts(node) {\n    const fonts = new Set();\n    function traverse(node) {\n        const fontFamily = node.style.fontFamily || getComputedStyle(node).fontFamily;\n        fontFamily.split(',').forEach((font) => {\n            fonts.add(normalizeFontFamily(font));\n        });\n        Array.from(node.children).forEach((child) => {\n            if (child instanceof HTMLElement) {\n                traverse(child);\n            }\n        });\n    }\n    traverse(node);\n    return fonts;\n}\nasync function getWebFontCSS(node, options) {\n    const rules = await parseWebFontRules(node, options);\n    const usedFonts = getUsedFonts(node);\n    const cssTexts = await Promise.all(rules\n        .filter((rule) => usedFonts.has(normalizeFontFamily(rule.style.fontFamily)))\n        .map((rule) => {\n        const baseUrl = rule.parentStyleSheet\n            ? rule.parentStyleSheet.href\n            : null;\n        return (0,_embed_resources__WEBPACK_IMPORTED_MODULE_2__.embedResources)(rule.cssText, baseUrl, options);\n    }));\n    return cssTexts.join('\\n');\n}\nasync function embedWebFonts(clonedNode, options) {\n    const cssText = options.fontEmbedCSS != null\n        ? options.fontEmbedCSS\n        : options.skipFonts\n            ? null\n            : await getWebFontCSS(clonedNode, options);\n    if (cssText) {\n        const styleNode = document.createElement('style');\n        const sytleContent = document.createTextNode(cssText);\n        styleNode.appendChild(sytleContent);\n        if (clonedNode.firstChild) {\n            clonedNode.insertBefore(styleNode, clonedNode.firstChild);\n        }\n        else {\n            clonedNode.appendChild(styleNode);\n        }\n    }\n}\n//# sourceMappingURL=embed-webfonts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/embed-webfonts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/index.js":
/*!************************************************!*\
  !*** ./node_modules/html-to-image/es/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFontEmbedCSS: () => (/* binding */ getFontEmbedCSS),\n/* harmony export */   toBlob: () => (/* binding */ toBlob),\n/* harmony export */   toCanvas: () => (/* binding */ toCanvas),\n/* harmony export */   toJpeg: () => (/* binding */ toJpeg),\n/* harmony export */   toPixelData: () => (/* binding */ toPixelData),\n/* harmony export */   toPng: () => (/* binding */ toPng),\n/* harmony export */   toSvg: () => (/* binding */ toSvg)\n/* harmony export */ });\n/* harmony import */ var _clone_node__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./clone-node */ \"(ssr)/./node_modules/html-to-image/es/clone-node.js\");\n/* harmony import */ var _embed_images__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./embed-images */ \"(ssr)/./node_modules/html-to-image/es/embed-images.js\");\n/* harmony import */ var _apply_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./apply-style */ \"(ssr)/./node_modules/html-to-image/es/apply-style.js\");\n/* harmony import */ var _embed_webfonts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./embed-webfonts */ \"(ssr)/./node_modules/html-to-image/es/embed-webfonts.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/html-to-image/es/util.js\");\n\n\n\n\n\nasync function toSvg(node, options = {}) {\n    const { width, height } = (0,_util__WEBPACK_IMPORTED_MODULE_4__.getImageSize)(node, options);\n    const clonedNode = (await (0,_clone_node__WEBPACK_IMPORTED_MODULE_0__.cloneNode)(node, options, true));\n    await (0,_embed_webfonts__WEBPACK_IMPORTED_MODULE_3__.embedWebFonts)(clonedNode, options);\n    await (0,_embed_images__WEBPACK_IMPORTED_MODULE_1__.embedImages)(clonedNode, options);\n    (0,_apply_style__WEBPACK_IMPORTED_MODULE_2__.applyStyle)(clonedNode, options);\n    const datauri = await (0,_util__WEBPACK_IMPORTED_MODULE_4__.nodeToDataURL)(clonedNode, width, height);\n    return datauri;\n}\nasync function toCanvas(node, options = {}) {\n    const { width, height } = (0,_util__WEBPACK_IMPORTED_MODULE_4__.getImageSize)(node, options);\n    const svg = await toSvg(node, options);\n    const img = await (0,_util__WEBPACK_IMPORTED_MODULE_4__.createImage)(svg);\n    const canvas = document.createElement('canvas');\n    const context = canvas.getContext('2d');\n    const ratio = options.pixelRatio || (0,_util__WEBPACK_IMPORTED_MODULE_4__.getPixelRatio)();\n    const canvasWidth = options.canvasWidth || width;\n    const canvasHeight = options.canvasHeight || height;\n    canvas.width = canvasWidth * ratio;\n    canvas.height = canvasHeight * ratio;\n    if (!options.skipAutoScale) {\n        (0,_util__WEBPACK_IMPORTED_MODULE_4__.checkCanvasDimensions)(canvas);\n    }\n    canvas.style.width = `${canvasWidth}`;\n    canvas.style.height = `${canvasHeight}`;\n    if (options.backgroundColor) {\n        context.fillStyle = options.backgroundColor;\n        context.fillRect(0, 0, canvas.width, canvas.height);\n    }\n    context.drawImage(img, 0, 0, canvas.width, canvas.height);\n    return canvas;\n}\nasync function toPixelData(node, options = {}) {\n    const { width, height } = (0,_util__WEBPACK_IMPORTED_MODULE_4__.getImageSize)(node, options);\n    const canvas = await toCanvas(node, options);\n    const ctx = canvas.getContext('2d');\n    return ctx.getImageData(0, 0, width, height).data;\n}\nasync function toPng(node, options = {}) {\n    const canvas = await toCanvas(node, options);\n    return canvas.toDataURL();\n}\nasync function toJpeg(node, options = {}) {\n    const canvas = await toCanvas(node, options);\n    return canvas.toDataURL('image/jpeg', options.quality || 1);\n}\nasync function toBlob(node, options = {}) {\n    const canvas = await toCanvas(node, options);\n    const blob = await (0,_util__WEBPACK_IMPORTED_MODULE_4__.canvasToBlob)(canvas);\n    return blob;\n}\nasync function getFontEmbedCSS(node, options = {}) {\n    return (0,_embed_webfonts__WEBPACK_IMPORTED_MODULE_3__.getWebFontCSS)(node, options);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/mimes.js":
/*!************************************************!*\
  !*** ./node_modules/html-to-image/es/mimes.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMimeType: () => (/* binding */ getMimeType)\n/* harmony export */ });\nconst WOFF = 'application/font-woff';\nconst JPEG = 'image/jpeg';\nconst mimes = {\n    woff: WOFF,\n    woff2: WOFF,\n    ttf: 'application/font-truetype',\n    eot: 'application/vnd.ms-fontobject',\n    png: 'image/png',\n    jpg: JPEG,\n    jpeg: JPEG,\n    gif: 'image/gif',\n    tiff: 'image/tiff',\n    svg: 'image/svg+xml',\n    webp: 'image/webp',\n};\nfunction getExtension(url) {\n    const match = /\\.([^./]*?)$/g.exec(url);\n    return match ? match[1] : '';\n}\nfunction getMimeType(url) {\n    const extension = getExtension(url).toLowerCase();\n    return mimes[extension] || '';\n}\n//# sourceMappingURL=mimes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbC10by1pbWFnZS9lcy9taW1lcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGluZ2FwXFxPbmVEcml2ZVxcU3RhbGluaXMga29tcGl1dGVyaXNcXFJlYWN0LUJyaWNrc1xcZmxvcmlzdC1wcm9qZWN0XFx0b21lbGlhLWdlbGVlc1xcbm9kZV9tb2R1bGVzXFxodG1sLXRvLWltYWdlXFxlc1xcbWltZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgV09GRiA9ICdhcHBsaWNhdGlvbi9mb250LXdvZmYnO1xuY29uc3QgSlBFRyA9ICdpbWFnZS9qcGVnJztcbmNvbnN0IG1pbWVzID0ge1xuICAgIHdvZmY6IFdPRkYsXG4gICAgd29mZjI6IFdPRkYsXG4gICAgdHRmOiAnYXBwbGljYXRpb24vZm9udC10cnVldHlwZScsXG4gICAgZW90OiAnYXBwbGljYXRpb24vdm5kLm1zLWZvbnRvYmplY3QnLFxuICAgIHBuZzogJ2ltYWdlL3BuZycsXG4gICAganBnOiBKUEVHLFxuICAgIGpwZWc6IEpQRUcsXG4gICAgZ2lmOiAnaW1hZ2UvZ2lmJyxcbiAgICB0aWZmOiAnaW1hZ2UvdGlmZicsXG4gICAgc3ZnOiAnaW1hZ2Uvc3ZnK3htbCcsXG4gICAgd2VicDogJ2ltYWdlL3dlYnAnLFxufTtcbmZ1bmN0aW9uIGdldEV4dGVuc2lvbih1cmwpIHtcbiAgICBjb25zdCBtYXRjaCA9IC9cXC4oW14uL10qPykkL2cuZXhlYyh1cmwpO1xuICAgIHJldHVybiBtYXRjaCA/IG1hdGNoWzFdIDogJyc7XG59XG5leHBvcnQgZnVuY3Rpb24gZ2V0TWltZVR5cGUodXJsKSB7XG4gICAgY29uc3QgZXh0ZW5zaW9uID0gZ2V0RXh0ZW5zaW9uKHVybCkudG9Mb3dlckNhc2UoKTtcbiAgICByZXR1cm4gbWltZXNbZXh0ZW5zaW9uXSB8fCAnJztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1pbWVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/mimes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/html-to-image/es/util.js":
/*!***********************************************!*\
  !*** ./node_modules/html-to-image/es/util.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canvasToBlob: () => (/* binding */ canvasToBlob),\n/* harmony export */   checkCanvasDimensions: () => (/* binding */ checkCanvasDimensions),\n/* harmony export */   createImage: () => (/* binding */ createImage),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   getImageSize: () => (/* binding */ getImageSize),\n/* harmony export */   getPixelRatio: () => (/* binding */ getPixelRatio),\n/* harmony export */   getStyleProperties: () => (/* binding */ getStyleProperties),\n/* harmony export */   isInstanceOfElement: () => (/* binding */ isInstanceOfElement),\n/* harmony export */   nodeToDataURL: () => (/* binding */ nodeToDataURL),\n/* harmony export */   resolveUrl: () => (/* binding */ resolveUrl),\n/* harmony export */   svgToDataURL: () => (/* binding */ svgToDataURL),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nfunction resolveUrl(url, baseUrl) {\n    // url is absolute already\n    if (url.match(/^[a-z]+:\\/\\//i)) {\n        return url;\n    }\n    // url is absolute already, without protocol\n    if (url.match(/^\\/\\//)) {\n        return window.location.protocol + url;\n    }\n    // dataURI, mailto:, tel:, etc.\n    if (url.match(/^[a-z]+:/i)) {\n        return url;\n    }\n    const doc = document.implementation.createHTMLDocument();\n    const base = doc.createElement('base');\n    const a = doc.createElement('a');\n    doc.head.appendChild(base);\n    doc.body.appendChild(a);\n    if (baseUrl) {\n        base.href = baseUrl;\n    }\n    a.href = url;\n    return a.href;\n}\nconst uuid = (() => {\n    // generate uuid for className of pseudo elements.\n    // We should not use GUIDs, otherwise pseudo elements sometimes cannot be captured.\n    let counter = 0;\n    // ref: http://stackoverflow.com/a/6248722/2519373\n    const random = () => \n    // eslint-disable-next-line no-bitwise\n    `0000${((Math.random() * 36 ** 4) << 0).toString(36)}`.slice(-4);\n    return () => {\n        counter += 1;\n        return `u${random()}${counter}`;\n    };\n})();\nfunction delay(ms) {\n    return (args) => new Promise((resolve) => {\n        setTimeout(() => resolve(args), ms);\n    });\n}\nfunction toArray(arrayLike) {\n    const arr = [];\n    for (let i = 0, l = arrayLike.length; i < l; i++) {\n        arr.push(arrayLike[i]);\n    }\n    return arr;\n}\nlet styleProps = null;\nfunction getStyleProperties(options = {}) {\n    if (styleProps) {\n        return styleProps;\n    }\n    if (options.includeStyleProperties) {\n        styleProps = options.includeStyleProperties;\n        return styleProps;\n    }\n    styleProps = toArray(window.getComputedStyle(document.documentElement));\n    return styleProps;\n}\nfunction px(node, styleProperty) {\n    const win = node.ownerDocument.defaultView || window;\n    const val = win.getComputedStyle(node).getPropertyValue(styleProperty);\n    return val ? parseFloat(val.replace('px', '')) : 0;\n}\nfunction getNodeWidth(node) {\n    const leftBorder = px(node, 'border-left-width');\n    const rightBorder = px(node, 'border-right-width');\n    return node.clientWidth + leftBorder + rightBorder;\n}\nfunction getNodeHeight(node) {\n    const topBorder = px(node, 'border-top-width');\n    const bottomBorder = px(node, 'border-bottom-width');\n    return node.clientHeight + topBorder + bottomBorder;\n}\nfunction getImageSize(targetNode, options = {}) {\n    const width = options.width || getNodeWidth(targetNode);\n    const height = options.height || getNodeHeight(targetNode);\n    return { width, height };\n}\nfunction getPixelRatio() {\n    let ratio;\n    let FINAL_PROCESS;\n    try {\n        FINAL_PROCESS = process;\n    }\n    catch (e) {\n        // pass\n    }\n    const val = FINAL_PROCESS && FINAL_PROCESS.env\n        ? FINAL_PROCESS.env.devicePixelRatio\n        : null;\n    if (val) {\n        ratio = parseInt(val, 10);\n        if (Number.isNaN(ratio)) {\n            ratio = 1;\n        }\n    }\n    return ratio || window.devicePixelRatio || 1;\n}\n// @see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/canvas#maximum_canvas_size\nconst canvasDimensionLimit = 16384;\nfunction checkCanvasDimensions(canvas) {\n    if (canvas.width > canvasDimensionLimit ||\n        canvas.height > canvasDimensionLimit) {\n        if (canvas.width > canvasDimensionLimit &&\n            canvas.height > canvasDimensionLimit) {\n            if (canvas.width > canvas.height) {\n                canvas.height *= canvasDimensionLimit / canvas.width;\n                canvas.width = canvasDimensionLimit;\n            }\n            else {\n                canvas.width *= canvasDimensionLimit / canvas.height;\n                canvas.height = canvasDimensionLimit;\n            }\n        }\n        else if (canvas.width > canvasDimensionLimit) {\n            canvas.height *= canvasDimensionLimit / canvas.width;\n            canvas.width = canvasDimensionLimit;\n        }\n        else {\n            canvas.width *= canvasDimensionLimit / canvas.height;\n            canvas.height = canvasDimensionLimit;\n        }\n    }\n}\nfunction canvasToBlob(canvas, options = {}) {\n    if (canvas.toBlob) {\n        return new Promise((resolve) => {\n            canvas.toBlob(resolve, options.type ? options.type : 'image/png', options.quality ? options.quality : 1);\n        });\n    }\n    return new Promise((resolve) => {\n        const binaryString = window.atob(canvas\n            .toDataURL(options.type ? options.type : undefined, options.quality ? options.quality : undefined)\n            .split(',')[1]);\n        const len = binaryString.length;\n        const binaryArray = new Uint8Array(len);\n        for (let i = 0; i < len; i += 1) {\n            binaryArray[i] = binaryString.charCodeAt(i);\n        }\n        resolve(new Blob([binaryArray], {\n            type: options.type ? options.type : 'image/png',\n        }));\n    });\n}\nfunction createImage(url) {\n    return new Promise((resolve, reject) => {\n        const img = new Image();\n        img.onload = () => {\n            img.decode().then(() => {\n                requestAnimationFrame(() => resolve(img));\n            });\n        };\n        img.onerror = reject;\n        img.crossOrigin = 'anonymous';\n        img.decoding = 'async';\n        img.src = url;\n    });\n}\nasync function svgToDataURL(svg) {\n    return Promise.resolve()\n        .then(() => new XMLSerializer().serializeToString(svg))\n        .then(encodeURIComponent)\n        .then((html) => `data:image/svg+xml;charset=utf-8,${html}`);\n}\nasync function nodeToDataURL(node, width, height) {\n    const xmlns = 'http://www.w3.org/2000/svg';\n    const svg = document.createElementNS(xmlns, 'svg');\n    const foreignObject = document.createElementNS(xmlns, 'foreignObject');\n    svg.setAttribute('width', `${width}`);\n    svg.setAttribute('height', `${height}`);\n    svg.setAttribute('viewBox', `0 0 ${width} ${height}`);\n    foreignObject.setAttribute('width', '100%');\n    foreignObject.setAttribute('height', '100%');\n    foreignObject.setAttribute('x', '0');\n    foreignObject.setAttribute('y', '0');\n    foreignObject.setAttribute('externalResourcesRequired', 'true');\n    svg.appendChild(foreignObject);\n    foreignObject.appendChild(node);\n    return svgToDataURL(svg);\n}\nconst isInstanceOfElement = (node, instance) => {\n    if (node instanceof instance)\n        return true;\n    const nodePrototype = Object.getPrototypeOf(node);\n    if (nodePrototype === null)\n        return false;\n    return (nodePrototype.constructor.name === instance.name ||\n        isInstanceOfElement(nodePrototype, instance));\n};\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-to-image/es/util.js\n");

/***/ })

};
;