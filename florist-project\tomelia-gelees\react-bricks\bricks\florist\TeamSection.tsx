import React from 'react'
import { Text, RichText, Image, Repeater, types } from 'react-bricks/rsc'
import classNames from 'classnames'

interface TeamSectionProps {
  backgroundColor: types.IColor
  layout: 'grid' | 'carousel'
  columns: 2 | 3 | 4
}

const TeamSection: types.Brick<TeamSectionProps> = ({
  backgroundColor = { color: '#ffffff', className: 'bg-white' },
  layout = 'grid',
  columns = 3,
}) => {
  const gridClasses = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  }

  return (
    <section
      className={classNames('py-16', backgroundColor?.className)}
      style={{ backgroundColor: backgroundColor?.color }}
    >
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <Text
            propName="sectionSubtitle"
            placeholder="The best florist crew around"
            renderBlock={(props) => (
              <p className="text-green-600 font-medium text-sm uppercase tracking-wide mb-4">
                {props.children}
              </p>
            )}
          />
          
          <Text
            propName="sectionTitle"
            placeholder="Meet the Team That Makes Miracles Happen"
            renderBlock={(props) => (
              <h2 className="text-4xl md:text-5xl font-light text-gray-900 mb-6">
                {props.children}
              </h2>
            )}
          />
          
          <RichText
            propName="sectionDescription"
            placeholder="Our passionate team of floral artists and designers brings years of experience and creativity to every arrangement."
            renderBlock={(props) => (
              <div className="text-lg text-gray-600 max-w-2xl mx-auto">
                {props.children}
              </div>
            )}
            allowedFeatures={[
              types.RichTextFeatures.Bold,
              types.RichTextFeatures.Italic,
            ]}
          />
        </div>

        {/* Team Grid */}
        <div className={classNames('grid gap-8', gridClasses[columns])}>
          <Repeater
            propName="teamMembers"
            itemProps={{
              layout: layout,
            }}
          />
        </div>
      </div>
    </section>
  )
}

// Team Member Component
interface TeamMemberProps {
  layout: 'grid' | 'carousel'
}

const TeamMember: types.Brick<TeamMemberProps> = ({ layout = 'grid' }) => {
  return (
    <div className="group text-center">
      {/* Photo */}
      <div className="relative mb-6 overflow-hidden">
        <Image
          propName="memberPhoto"
          alt="Team member"
          imageClassName="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-500"
        />
        
        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-green-600 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
      </div>

      {/* Member Info */}
      <div className="space-y-2">
        {/* Name */}
        <Text
          propName="memberName"
          placeholder="Elizabeth Morris"
          renderBlock={(props) => (
            <h3 className="text-xl font-medium text-gray-900">
              {props.children}
            </h3>
          )}
        />

        {/* Role */}
        <Text
          propName="memberRole"
          placeholder="Florist"
          renderBlock={(props) => (
            <p className="text-green-600 font-medium">
              {props.children}
            </p>
          )}
        />

        {/* Bio */}
        <RichText
          propName="memberBio"
          placeholder="Passionate about creating beautiful arrangements that bring joy to every occasion."
          renderBlock={(props) => (
            <div className="text-gray-600 text-sm leading-relaxed">
              {props.children}
            </div>
          )}
          allowedFeatures={[
            types.RichTextFeatures.Bold,
            types.RichTextFeatures.Italic,
          ]}
        />

        {/* Social Links */}
        <div className="flex justify-center space-x-4 pt-4">
          <Text
            propName="instagramHandle"
            placeholder="@elizabeth_florist"
            renderBlock={(props) => (
              <a
                href={`https://instagram.com/${props.children.replace('@', '')}`}
                className="text-gray-400 hover:text-green-600 transition-colors duration-200"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
            )}
          />
        </div>
      </div>
    </div>
  )
}

TeamMember.schema = {
  name: 'team-member',
  label: 'Team Member',
  category: 'Florist',
  tags: ['team', 'member', 'person', 'staff'],
  
  getDefaultProps: () => ({
    layout: 'grid',
    memberName: 'Elizabeth Morris',
    memberRole: 'Florist',
    memberBio: 'Passionate about creating beautiful arrangements that bring joy to every occasion.',
    instagramHandle: '@elizabeth_florist',
  }),

  sideEditProps: [
    {
      name: 'layout',
      label: 'Layout Style',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 'grid', label: 'Grid' },
          { value: 'carousel', label: 'Carousel' },
        ],
      },
    },
  ],
}

TeamSection.schema = {
  name: 'team-section',
  label: 'Team Section',
  category: 'Florist',
  tags: ['team', 'about', 'staff', 'florist'],
  previewImageUrl: '/api/preview/team-section.png',
  
  getDefaultProps: () => ({
    backgroundColor: { color: '#ffffff', className: 'bg-white' },
    layout: 'grid',
    columns: 3,
    sectionSubtitle: 'The best florist crew around',
    sectionTitle: 'Meet the Team That Makes Miracles Happen',
    sectionDescription: 'Our passionate team of floral artists and designers brings years of experience and creativity to every arrangement.',
    teamMembers: [
      {
        memberName: 'Velva Kopf',
        memberRole: 'Biologist',
        memberBio: 'Expert in plant biology and flower care with over 10 years of experience.',
        instagramHandle: '@velva_plants',
        layout: 'grid',
      },
      {
        memberName: 'Elizabeth Morris',
        memberRole: 'Florist',
        memberBio: 'Master florist specializing in wedding and event arrangements.',
        instagramHandle: '@elizabeth_florist',
        layout: 'grid',
      },
      {
        memberName: 'Blaine Bush',
        memberRole: 'Photographer',
        memberBio: 'Captures the beauty of our floral creations through stunning photography.',
        instagramHandle: '@blaine_photos',
        layout: 'grid',
      },
    ],
  }),

  sideEditProps: [
    {
      name: 'backgroundColor',
      label: 'Background Color',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Color,
        options: [
          { value: { color: '#ffffff', className: 'bg-white' }, label: 'White' },
          { value: { color: '#f8f9fa', className: 'bg-gray-50' }, label: 'Light Gray' },
          { value: { color: '#f0f8f0', className: 'bg-green-25' }, label: 'Light Green' },
        ],
      },
    },
    {
      name: 'layout',
      label: 'Layout Style',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 'grid', label: 'Grid' },
          { value: 'carousel', label: 'Carousel' },
        ],
      },
    },
    {
      name: 'columns',
      label: 'Grid Columns',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 2, label: '2 Columns' },
          { value: 3, label: '3 Columns' },
          { value: 4, label: '4 Columns' },
        ],
      },
    },
  ],

  repeaterItems: [
    {
      name: 'teamMembers',
      itemType: 'team-member',
      itemLabel: 'Team Member',
      min: 1,
      max: 8,
    },
  ],
}

export { TeamMember }
export default TeamSection
