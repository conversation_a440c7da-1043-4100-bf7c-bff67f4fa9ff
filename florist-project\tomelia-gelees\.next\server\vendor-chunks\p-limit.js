"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/p-limit";
exports.ids = ["vendor-chunks/p-limit"];
exports.modules = {

/***/ "(ssr)/./node_modules/p-limit/index.js":
/*!***************************************!*\
  !*** ./node_modules/p-limit/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Queue = __webpack_require__(/*! yocto-queue */ \"(ssr)/./node_modules/yocto-queue/index.js\");\n\nconst pLimit = concurrency => {\n\tif (!((Number.isInteger(concurrency) || concurrency === Infinity) && concurrency > 0)) {\n\t\tthrow new TypeError('Expected `concurrency` to be a number from 1 and up');\n\t}\n\n\tconst queue = new Queue();\n\tlet activeCount = 0;\n\n\tconst next = () => {\n\t\tactiveCount--;\n\n\t\tif (queue.size > 0) {\n\t\t\tqueue.dequeue()();\n\t\t}\n\t};\n\n\tconst run = async (fn, resolve, ...args) => {\n\t\tactiveCount++;\n\n\t\tconst result = (async () => fn(...args))();\n\n\t\tresolve(result);\n\n\t\ttry {\n\t\t\tawait result;\n\t\t} catch {}\n\n\t\tnext();\n\t};\n\n\tconst enqueue = (fn, resolve, ...args) => {\n\t\tqueue.enqueue(run.bind(null, fn, resolve, ...args));\n\n\t\t(async () => {\n\t\t\t// This function needs to wait until the next microtask before comparing\n\t\t\t// `activeCount` to `concurrency`, because `activeCount` is updated asynchronously\n\t\t\t// when the run function is dequeued and called. The comparison in the if-statement\n\t\t\t// needs to happen asynchronously as well to get an up-to-date value for `activeCount`.\n\t\t\tawait Promise.resolve();\n\n\t\t\tif (activeCount < concurrency && queue.size > 0) {\n\t\t\t\tqueue.dequeue()();\n\t\t\t}\n\t\t})();\n\t};\n\n\tconst generator = (fn, ...args) => new Promise(resolve => {\n\t\tenqueue(fn, resolve, ...args);\n\t});\n\n\tObject.defineProperties(generator, {\n\t\tactiveCount: {\n\t\t\tget: () => activeCount\n\t\t},\n\t\tpendingCount: {\n\t\t\tget: () => queue.size\n\t\t},\n\t\tclearQueue: {\n\t\t\tvalue: () => {\n\t\t\t\tqueue.clear();\n\t\t\t}\n\t\t}\n\t});\n\n\treturn generator;\n};\n\nmodule.exports = pLimit;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/p-limit/index.js\n");

/***/ })

};
;