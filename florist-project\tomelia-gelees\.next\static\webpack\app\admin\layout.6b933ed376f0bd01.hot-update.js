"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./react-bricks/bricks/florist/TeamSection.tsx":
/*!*****************************************************!*\
  !*** ./react-bricks/bricks/florist/TeamSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamMember: () => (/* binding */ TeamMember),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-bricks/rsc */ \"(app-pages-browser)/./node_modules/react-bricks/rsc/react-bricks-rsc.esm.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst TeamSection = (param)=>{\n    let { backgroundColor = {\n        color: '#ffffff',\n        className: 'bg-white'\n    }, layout = 'grid', columns = 3 } = param;\n    const gridClasses = {\n        2: 'grid-cols-1 md:grid-cols-2',\n        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_3___default()('py-16', backgroundColor === null || backgroundColor === void 0 ? void 0 : backgroundColor.className),\n        style: {\n            backgroundColor: backgroundColor === null || backgroundColor === void 0 ? void 0 : backgroundColor.color\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            propName: \"sectionSubtitle\",\n                            placeholder: \"The best florist crew around\",\n                            renderBlock: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600 font-medium text-sm uppercase tracking-wide mb-4\",\n                                    children: props.children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            propName: \"sectionTitle\",\n                            placeholder: \"Meet the Team That Makes Miracles Happen\",\n                            renderBlock: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl md:text-5xl font-light text-gray-900 mb-6\",\n                                    children: props.children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.RichText, {\n                            propName: \"sectionDescription\",\n                            placeholder: \"Our passionate team of floral artists and designers brings years of experience and creativity to every arrangement.\",\n                            renderBlock: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: props.children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, void 0),\n                            allowedFeatures: [\n                                react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.RichTextFeatures.Bold,\n                                react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.RichTextFeatures.Italic\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()('grid gap-8', gridClasses[columns]),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.Repeater, {\n                        propName: \"teamMembers\",\n                        itemProps: {\n                            layout: layout\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TeamSection;\nconst TeamMember = (param)=>{\n    let { layout = 'grid' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mb-6 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.Image, {\n                        propName: \"memberPhoto\",\n                        alt: \"Team member\",\n                        imageClassName: \"w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-green-600 bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        propName: \"memberName\",\n                        placeholder: \"Elizabeth Morris\",\n                        renderBlock: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-medium text-gray-900\",\n                                children: props.children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        propName: \"memberRole\",\n                        placeholder: \"Florist\",\n                        renderBlock: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-600 font-medium\",\n                                children: props.children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.RichText, {\n                        propName: \"memberBio\",\n                        placeholder: \"Passionate about creating beautiful arrangements that bring joy to every occasion.\",\n                        renderBlock: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600 text-sm leading-relaxed\",\n                                children: props.children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, void 0),\n                        allowedFeatures: [\n                            react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.RichTextFeatures.Bold,\n                            react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.RichTextFeatures.Italic\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center space-x-4 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            propName: \"instagramHandle\",\n                            placeholder: \"@elizabeth_florist\",\n                            renderBlock: (props)=>{\n                                const handle = typeof props.children === 'string' ? props.children : '@elizabeth_florist';\n                                const cleanHandle = handle.replace('@', '');\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://instagram.com/\".concat(cleanHandle),\n                                    className: \"text-gray-400 hover:text-green-600 transition-colors duration-200\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Stalinis kompiuteris\\\\React-Bricks\\\\florist-project\\\\tomelia-gelees\\\\react-bricks\\\\bricks\\\\florist\\\\TeamSection.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TeamMember;\nTeamMember.schema = {\n    name: 'team-member',\n    label: 'Team Member',\n    category: 'Florist',\n    tags: [\n        'team',\n        'member',\n        'person',\n        'staff'\n    ],\n    getDefaultProps: ()=>({\n            layout: 'grid',\n            memberName: 'Elizabeth Morris',\n            memberRole: 'Florist',\n            memberBio: 'Passionate about creating beautiful arrangements that bring joy to every occasion.',\n            instagramHandle: '@elizabeth_florist'\n        }),\n    sideEditProps: [\n        {\n            name: 'layout',\n            label: 'Layout Style',\n            type: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.SideEditPropType.Select,\n            selectOptions: {\n                display: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.OptionsDisplay.Select,\n                options: [\n                    {\n                        value: 'grid',\n                        label: 'Grid'\n                    },\n                    {\n                        value: 'carousel',\n                        label: 'Carousel'\n                    }\n                ]\n            }\n        }\n    ]\n};\nTeamSection.schema = {\n    name: 'team-section',\n    label: 'Team Section',\n    category: 'Florist',\n    tags: [\n        'team',\n        'about',\n        'staff',\n        'florist'\n    ],\n    previewImageUrl: '/api/preview/team-section.png',\n    getDefaultProps: ()=>({\n            backgroundColor: {\n                color: '#ffffff',\n                className: 'bg-white'\n            },\n            layout: 'grid',\n            columns: 3,\n            sectionSubtitle: 'The best florist crew around',\n            sectionTitle: 'Meet the Team That Makes Miracles Happen',\n            sectionDescription: 'Our passionate team of floral artists and designers brings years of experience and creativity to every arrangement.',\n            teamMembers: [\n                {\n                    memberName: 'Velva Kopf',\n                    memberRole: 'Biologist',\n                    memberBio: 'Expert in plant biology and flower care with over 10 years of experience.',\n                    instagramHandle: '@velva_plants',\n                    layout: 'grid'\n                },\n                {\n                    memberName: 'Elizabeth Morris',\n                    memberRole: 'Florist',\n                    memberBio: 'Master florist specializing in wedding and event arrangements.',\n                    instagramHandle: '@elizabeth_florist',\n                    layout: 'grid'\n                },\n                {\n                    memberName: 'Blaine Bush',\n                    memberRole: 'Photographer',\n                    memberBio: 'Captures the beauty of our floral creations through stunning photography.',\n                    instagramHandle: '@blaine_photos',\n                    layout: 'grid'\n                }\n            ]\n        }),\n    sideEditProps: [\n        {\n            name: 'backgroundColor',\n            label: 'Background Color',\n            type: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.SideEditPropType.Select,\n            selectOptions: {\n                display: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.OptionsDisplay.Color,\n                options: [\n                    {\n                        value: {\n                            color: '#ffffff',\n                            className: 'bg-white'\n                        },\n                        label: 'White'\n                    },\n                    {\n                        value: {\n                            color: '#f8f9fa',\n                            className: 'bg-gray-50'\n                        },\n                        label: 'Light Gray'\n                    },\n                    {\n                        value: {\n                            color: '#f0f8f0',\n                            className: 'bg-green-25'\n                        },\n                        label: 'Light Green'\n                    }\n                ]\n            }\n        },\n        {\n            name: 'layout',\n            label: 'Layout Style',\n            type: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.SideEditPropType.Select,\n            selectOptions: {\n                display: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.OptionsDisplay.Select,\n                options: [\n                    {\n                        value: 'grid',\n                        label: 'Grid'\n                    },\n                    {\n                        value: 'carousel',\n                        label: 'Carousel'\n                    }\n                ]\n            }\n        },\n        {\n            name: 'columns',\n            label: 'Grid Columns',\n            type: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.SideEditPropType.Select,\n            selectOptions: {\n                display: react_bricks_rsc__WEBPACK_IMPORTED_MODULE_2__.types.OptionsDisplay.Select,\n                options: [\n                    {\n                        value: 2,\n                        label: '2 Columns'\n                    },\n                    {\n                        value: 3,\n                        label: '3 Columns'\n                    },\n                    {\n                        value: 4,\n                        label: '4 Columns'\n                    }\n                ]\n            }\n        }\n    ],\n    repeaterItems: [\n        {\n            name: 'teamMembers',\n            itemType: 'team-member',\n            itemLabel: 'Team Member',\n            min: 1,\n            max: 8\n        }\n    ]\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TeamSection);\nvar _c, _c1;\n$RefreshReg$(_c, \"TeamSection\");\n$RefreshReg$(_c1, \"TeamMember\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./react-bricks/bricks/florist/TeamSection.tsx\n"));

/***/ })

});