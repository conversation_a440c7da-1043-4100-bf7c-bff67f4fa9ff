{"kind": "FETCH", "data": {"headers": {"access-control-expose-headers": "WWW-Authenticate,Server-Authorization,x-total-count,x-total-size,x-total-media-size,x-total-deleted,x-total-root,x-ratelimit-limit,x-ratelimit-remaining,x-ratelimit-expire", "cache-control": "no-cache, max-age=1209600", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json; charset=utf-8", "date": "<PERSON><PERSON>, 22 Jul 2025 10:04:42 GMT", "expires": "Tue, 05 Aug 2025 10:04:42 GMT", "server": "nginx", "status": "200 OK", "transfer-encoding": "chunked", "vary": "origin,accept-encoding", "x-powered-by": "Phusion Passenger 6.0.6, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>k<PERSON><PERSON>"}, "body": "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", "status": 200, "url": "https://api.reactbricks.com/v2/app/pages/|/translations/en"}, "revalidate": 3, "tags": []}