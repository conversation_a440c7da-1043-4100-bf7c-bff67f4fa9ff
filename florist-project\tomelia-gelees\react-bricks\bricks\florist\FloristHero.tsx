import React from 'react'
import { Text, RichText, Image, types } from 'react-bricks/rsc'
import classNames from 'classnames'

interface FloristHeroProps {
  backgroundColor: types.IColor
  backgroundImage: types.IImageSource
  overlayOpacity: number
  textAlign: 'left' | 'center' | 'right'
  height: 'small' | 'medium' | 'large' | 'full'
}

const FloristHero: types.Brick<FloristHeroProps> = ({
  backgroundColor = { color: '#f8f9fa', className: 'bg-gray-50' },
  backgroundImage,
  overlayOpacity = 0.3,
  textAlign = 'center',
  height = 'large',
}) => {
  const heightClasses = {
    small: 'min-h-[400px]',
    medium: 'min-h-[600px]',
    large: 'min-h-[800px]',
    full: 'min-h-screen',
  }

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  }

  return (
    <section
      className={classNames(
        'relative flex items-center justify-center',
        heightClasses[height],
        backgroundColor?.className
      )}
      style={{ backgroundColor: backgroundColor?.color }}
    >
      {/* Background Image */}
      {backgroundImage && (
        <div className="absolute inset-0 z-0">
          <Image
            propName="backgroundImage"
            alt="Hero background"
            imageClassName="w-full h-full object-cover"
          />
          {/* Overlay */}
          <div
            className="absolute inset-0 bg-black"
            style={{ opacity: overlayOpacity }}
          />
        </div>
      )}

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-16">
        <div className={classNames('max-w-4xl mx-auto', textAlignClasses[textAlign])}>
          {/* Main Heading */}
          <Text
            propName="title"
            placeholder="Tomelia"
            renderBlock={(props) => (
              <h1 className="text-5xl md:text-7xl font-light text-white mb-4 tracking-wide">
                {props.children}
              </h1>
            )}
          />

          {/* Subtitle */}
          <Text
            propName="subtitle"
            placeholder="We Create Beauty Inspired by Flora"
            renderBlock={(props) => (
              <h2 className="text-2xl md:text-3xl font-light text-white mb-8 opacity-90">
                {props.children}
              </h2>
            )}
          />

          {/* Description */}
          <RichText
            propName="description"
            placeholder="Discover our exquisite collection of fresh flowers, elegant arrangements, and botanical artistry. Each creation tells a story of natural beauty and craftsmanship."
            renderBlock={(props) => (
              <div className="text-lg text-white opacity-80 mb-12 max-w-2xl mx-auto leading-relaxed">
                {props.children}
              </div>
            )}
            allowedFeatures={[
              types.RichTextFeatures.Bold,
              types.RichTextFeatures.Italic,
            ]}
          />

          {/* Call to Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Text
              propName="primaryButtonText"
              placeholder="Shop Arrangements"
              renderBlock={(props) => (
                <a
                  href="#products"
                  className="inline-block bg-white text-gray-900 px-8 py-4 rounded-none font-medium text-lg hover:bg-gray-100 transition-colors duration-300 min-w-[200px] text-center"
                >
                  {props.children}
                </a>
              )}
            />
            <Text
              propName="secondaryButtonText"
              placeholder="Contact Us"
              renderBlock={(props) => (
                <a
                  href="#contact"
                  className="inline-block border-2 border-white text-white px-8 py-4 rounded-none font-medium text-lg hover:bg-white hover:text-gray-900 transition-colors duration-300 min-w-[200px] text-center"
                >
                  {props.children}
                </a>
              )}
            />
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  )
}

FloristHero.schema = {
  name: 'florist-hero',
  label: 'Florist Hero Section',
  category: 'Florist',
  tags: ['hero', 'florist', 'banner'],
  previewImageUrl: '/api/preview/florist-hero.png',
  
  getDefaultProps: () => ({
    backgroundColor: { color: '#f8f9fa', className: 'bg-gray-50' },
    overlayOpacity: 0.4,
    textAlign: 'center',
    height: 'large',
    title: 'Tomelia',
    subtitle: 'We Create Beauty Inspired by Flora',
    description: 'Discover our exquisite collection of fresh flowers, elegant arrangements, and botanical artistry. Each creation tells a story of natural beauty and craftsmanship.',
    primaryButtonText: 'Shop Arrangements',
    secondaryButtonText: 'Contact Us',
  }),

  sideEditProps: [
    {
      name: 'backgroundColor',
      label: 'Background Color',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Color,
        options: [
          { value: { color: '#f8f9fa', className: 'bg-gray-50' }, label: 'Light Gray' },
          { value: { color: '#e8f5e8', className: 'bg-green-50' }, label: 'Light Green' },
          { value: { color: '#f0f8f0', className: 'bg-green-25' }, label: 'Mint' },
          { value: { color: '#ffffff', className: 'bg-white' }, label: 'White' },
        ],
      },
    },
    {
      name: 'overlayOpacity',
      label: 'Overlay Opacity',
      type: types.SideEditPropType.Range,
      rangeOptions: {
        min: 0,
        max: 0.8,
        step: 0.1,
      },
    },
    {
      name: 'textAlign',
      label: 'Text Alignment',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 'left', label: 'Left' },
          { value: 'center', label: 'Center' },
          { value: 'right', label: 'Right' },
        ],
      },
    },
    {
      name: 'height',
      label: 'Section Height',
      type: types.SideEditPropType.Select,
      selectOptions: {
        display: types.OptionsDisplay.Select,
        options: [
          { value: 'small', label: 'Small (400px)' },
          { value: 'medium', label: 'Medium (600px)' },
          { value: 'large', label: 'Large (800px)' },
          { value: 'full', label: 'Full Screen' },
        ],
      },
    },
  ],
}

export default FloristHero
