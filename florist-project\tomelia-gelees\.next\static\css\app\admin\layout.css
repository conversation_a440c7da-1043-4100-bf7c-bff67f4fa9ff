/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\admin\\ReactBricksApp.tsx","import":"Nunito_Sans","arguments":[{"subsets":["latin"],"display":"swap","weight":["300","400","600","700","800","900"],"style":["normal","italic"],"variable":"--font-nunito"}],"variableName":"nunito"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/3100a8542a3f85a9-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/8efafe65ca22a4a3-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/6fd65d4810b2abff-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/f1697282fed31d9e-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e6dbccc0d7f497be-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/3100a8542a3f85a9-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/8efafe65ca22a4a3-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/6fd65d4810b2abff-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/f1697282fed31d9e-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e6dbccc0d7f497be-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/3100a8542a3f85a9-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/8efafe65ca22a4a3-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/6fd65d4810b2abff-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/f1697282fed31d9e-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e6dbccc0d7f497be-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/3100a8542a3f85a9-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/8efafe65ca22a4a3-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/6fd65d4810b2abff-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/f1697282fed31d9e-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e6dbccc0d7f497be-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/3100a8542a3f85a9-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/8efafe65ca22a4a3-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/6fd65d4810b2abff-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/f1697282fed31d9e-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e6dbccc0d7f497be-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/3100a8542a3f85a9-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/8efafe65ca22a4a3-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/6fd65d4810b2abff-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/f1697282fed31d9e-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e6dbccc0d7f497be-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/aa1b27f9e325731f-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/bb1cdd1f059eb586-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e850464f9dfbb75b-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2e3d66f724b5d1ac-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c90a9e82ec3f8868-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/aa1b27f9e325731f-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/bb1cdd1f059eb586-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e850464f9dfbb75b-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2e3d66f724b5d1ac-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c90a9e82ec3f8868-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/aa1b27f9e325731f-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/bb1cdd1f059eb586-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e850464f9dfbb75b-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2e3d66f724b5d1ac-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c90a9e82ec3f8868-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/aa1b27f9e325731f-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/bb1cdd1f059eb586-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e850464f9dfbb75b-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2e3d66f724b5d1ac-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c90a9e82ec3f8868-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/aa1b27f9e325731f-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/bb1cdd1f059eb586-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e850464f9dfbb75b-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2e3d66f724b5d1ac-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 800;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c90a9e82ec3f8868-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/aa1b27f9e325731f-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/bb1cdd1f059eb586-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/e850464f9dfbb75b-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/2e3d66f724b5d1ac-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 900;
  font-stretch: 100%;
  font-display: swap;
  src: url(/_next/static/media/c90a9e82ec3f8868-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Nunito Sans Fallback';src: local("Arial");ascent-override: 99.71%;descent-override: 34.82%;line-gap-override: 0.00%;size-adjust: 101.39%
}.__className_669ad1 {font-family: 'Nunito Sans', 'Nunito Sans Fallback'
}.__variable_669ad1 {--font-nunito: 'Nunito Sans', 'Nunito Sans Fallback'
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/HeroUnit.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
.HeroUnit_container__XWwmk {
  background-color: white;
}

.dark .HeroUnit_container__XWwmk {
  background-color: rgb(17 24 39);
}

.HeroUnit_padding__BePY4 {
  max-width: 36rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.HeroUnit_bigPadding__KiuDc {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.HeroUnit_smallPadding__vZtze {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.HeroUnit_heroImage__ueCb3 {
  width: 5rem;
  margin-bottom: 1.25rem;
  margin-left: auto;
  margin-right: auto;
}

.HeroUnit_title__y8IGn {
  font-size: 1.875rem;
  line-height: 2.25rem;
  color: rgb(17 24 39);
  text-align: center;
  font-weight: 900;
  line-height: 1.25;
  margin-bottom: 0.75rem;
}

.dark .HeroUnit_title__y8IGn {
  color: rgb(255 255 255);
}

.HeroUnit_placeholderSpan__byIpr {
  opacity: 0.3;
}

.HeroUnit_richText___r9G0 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  text-align: center;
  line-height: 1.625;
  color: rgb(55 65 81);
}

.dark .HeroUnit_richText___r9G0 {
  color: rgb(243 244 246);
}

.HeroUnit_code__fGqAQ {
  font-size: 0.875rem;
  line-height: 1.25rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  background-color: rgb(229 231 235);
  border-radius: 0.25rem;
}

.dark .HeroUnit_code__fGqAQ {
  background-color: rgb(55 65 81);
}

.HeroUnit_richTextLink__1G9QA {
  color: rgb(14 165 233);
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.HeroUnit_richTextLink__1G9QA:hover {
  color: rgb(2 132 199);
}

@media (min-width: 640px) {
  .HeroUnit_title__y8IGn {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/Features.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
.Features_section__Turjx {
  background-color: white;
}

.dark .Features_section__Turjx {
  background-color: rgb(17 24 39);
}

.Features_container__yijaA {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.Features_sizeSmall__k5H1k {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.Features_sizeNormal__ITDbs {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

@media (min-width: 640px) {
  .Features_sizeSmall__k5H1k {
    margin-right: 16.66666%;
    margin-left: 16.66666%;
  }

  .Features_sizeNormal__ITDbs {
    margin-right: 5.55555%;
    margin-left: 5.55555%;
  }
}

@media (min-width: 1024px) {
  .Features_container__yijaA {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1280px) {
  .Features_sizeSmall__k5H1k {
    margin-right: 22.2222%;
    margin-left: 22.2222%;
  }
  .Features_sizeNormal__ITDbs {
    margin-right: 11.1111%;
    margin-left: 11.1111%;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/FeatureItem.module.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
.FeatureItem_cols2__kcG2l,
.FeatureItem_cols3__MkcJc,
.FeatureItem_cols4__nJAvV {
  margin-bottom: 3rem;
}

.FeatureItem_featureItemContainer__FXcwP {
  font-size: 1rem;
  line-height: 1.5rem;
}

.FeatureItem_imageClassName__jNzrw {
  display: block;
  width: 3rem;
  height: 3rem;
  object-fit: contain;
}

.FeatureItem_imageWrapper__L1jU6 {
  float: left;
  margin-right: 1.25rem;
  margin-top: 0.25rem;
}

.FeatureItem_textFeatureItemContainer__6S5Lf {
  overflow: hidden;
}

.FeatureItem_title___AyzR {
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: rgb(31 41 55);
}

.dark .FeatureItem_title___AyzR {
  color: white;
}

.FeatureItem_textColor__iF_7A {
  color: rgb(107 114 128);
}

.dark .FeatureItem_textColor__iF_7A {
  color: white;
}

.FeatureItem_linkContainer__3HN9v {
  margin-top: 0.5rem;
}

.FeatureItem_linkWrapper__IBWaa {
  cursor: pointer;
  color: rgb(14 165 233);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.FeatureItem_linkWrapper__IBWaa:hover {
  color: rgb(2 132 199);
  transform: translateY(-1px);
}

.FeatureItem_linkTextPlain1__9x5CH {
  display: flex;
  align-items: center;
}

.FeatureItem_linkTextPlain1__9x5CH > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.25rem;
}

.FeatureItem_svgClass__FFNh8 {
  width: 10px;
  height: 10px;
}

.FeatureItem_linkTextPlain2__3nyuK {
  display: inline-block;
}

.FeatureItem_linkTextPlain3__Hf172 {
  display: none;
}

@media (min-width: 640px) {
  .FeatureItem_cols2__kcG2l {
    flex: 0 1 45%;
    margin-bottom: 4rem;
  }

  .FeatureItem_cols3__MkcJc {
    flex: 0 1 27%;
    margin-bottom: 4rem;
  }

  .FeatureItem_cols4__nJAvV {
    flex: 0 1 45%;
    margin-bottom: 4rem;
  }

  .FeatureItem_imageWrapper__L1jU6 {
    float: none;
    margin-right: 0;
    margin-top: 0;
    margin-bottom: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .FeatureItem_cols4__nJAvV {
    flex: 0 1 20.1%;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/Button.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
.Button_buttonWrapper__4ypJF {
  display: inline-block;
  white-space: nowrap;
  text-align: center;
  border-radius: 9999px;
  font-weight: 700;
  line-height: 1;
  transition-property: all;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 150ms;
}

.Button_buttonWrapper__4ypJF:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  transform: translateY(-0.125rem);
}

.Button_buttonPsmall__bM5kF {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  min-width: 75px;
}

.Button_buttonPnormal__tadW_ {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  min-width: 120px;
}

.Button_buttonColorSolid__oqn0o {
  background-color: rgb(14 165 233);
  color: white;
}
.Button_buttonColorSolid__oqn0o:hover {
  background-color: rgb(2 132 199);
}

.Button_buttonColorOutline__DaUev {
  border-width: 1px;
  border-color: rgb(2 132 199);
  color: rgb(2 132 199);
}
.dark .Button_buttonColorOutline__DaUev {
  border-color: rgb(255 255 255);
  color: white;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/Footer.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
.Footer_section__wfJO6 {
  background-color: #f9fafb;
}

.dark .Footer_section__wfJO6 {
  background-color: rgb(17 24 39);
}

.Footer_container___Yr7D {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding-top: 3rem;
  padding-bottom: 3rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

@media (min-width: 640px) {
  .Footer_container___Yr7D {
    margin-left: 5.55555%;
    margin-right: 5.55555%;
  }
}

@media (min-width: 1280px) {
  .Footer_container___Yr7D {
    margin-left: 11.1111%;
    margin-right: 11.1111%;
  }
}

.Footer_elementsInfo__w_nPJ {
  width: 100%;
  margin-bottom: 3rem;
}

@media (min-width: 1024px) {
  .Footer_elementsInfo__w_nPJ {
    width: auto;
    margin-bottom: 0px;
    margin-right: 2rem;
  }

  .Footer_container___Yr7D {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

.Footer_linkLogo__USpto {
  display: block;
  margin-bottom: 1rem;
}

.Footer_imageLogo__eQH_H {
  width: 12rem;
  height: 1.75rem;
  object-fit: contain;
  object-position: left;
}

.Footer_paragraphRichText__bKDVb {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: rgb(107 114 128);
}

.dark .Footer_paragraphRichText__bKDVb {
  color: white;
}

.Footer_renderLink__zTgLO {
  color: rgb(14 165 233);
  transition-property: all;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 150ms;
}

.Footer_renderLink__zTgLO:hover {
  color: rgb(2 132 199);
  transform: translateY(-1px);
}


/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/FooterColumn.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
.FooterColumn_container__CLCPM {
  width: 50%;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .FooterColumn_container__CLCPM {
    width: auto;
    margin-right: 2rem;
  }
}

.FooterColumn_text__RrgMt {
  margin-bottom: 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 0.35em;
  min-width: 120px;
  color: rgb(156 163 175);
}

.dark .FooterColumn_text__RrgMt {
  color: rgb(243 244 246);
}

/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/FooterLink.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
.FooterLink_text__lqgtl {
  font-size: 0.875rem;
  line-height: 1.25rem;
  margin-bottom: 0.75rem;
  color: rgb(107 114 128);
  transition-property: all;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 150ms;
}

.FooterLink_text__lqgtl:hover {
  color: rgb(75 85 99);
  transform: translateY(-1px);
}

.dark .FooterLink_text__lqgtl {
  color: white;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/Header.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
.Header_section__2JJF4 {
  background-color: white;
}

.dark .Header_section__2JJF4 {
  background-color: rgb(17 24 39);
}

.Header_navClass__A860e {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.25rem;
}

.Header_linkLogo__moZLg {
  display: inline-flex;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  margin-right: 1.5rem;
}

.Header_imageClass___eQYQ {
  display: block;
  width: 8rem;
  height: 1.75rem;
  object-fit: contain;
  object-position: left;
}

.Header_containerMenuItems__sRsw0 {
  display: none;
  align-items: center;
}
.Header_containerMenuItems__sRsw0 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.5rem;
}

.Header_containerButtons__LQhDH {
  display: none;
  margin-left: auto;
}

.Header_buttonsWrapper__xXc5R {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.Header_buttonsWrapper__xXc5R > :not([hidden]) ~ :not([hidden]) {
  margin-left: 1.25rem;
}

.Header_containerHamburgerMenu___nWUB {
  position: relative;
  display: flex;
  height: 100%;
  align-items: center;
}

.Header_buttonHamburgerMenu__UQJik {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.25rem;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 5px;
}

.Header_buttonHamburgerMenu__UQJik:hover {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

.Header_buttonHamburgerMenu__UQJik:focus {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

.dark .Header_buttonHamburgerMenu__UQJik {
  background-color: rgb(17 24 39);
}

.dark .Header_buttonHamburgerMenu__UQJik:hover {
  background-color: rgb(14 165 233 / 0.4);
  color: white;
}

.dark .Header_buttonHamburgerMenu__UQJik:focus {
  background-color: rgb(14 165 233 / 0.4);
  color: white;
}

.Header_containerHamburgerMenuItems__jILh2 {
  position: absolute;
  top: 2rem;
  right: 0px;
  width: 16rem;
  background-color: white;
  padding: 1.25rem;
  border-width: 1px;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  z-index: 10;
}

.Header_darkModeButton__JU45P {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  margin-right: 1rem;
  margin-left: auto;
  cursor: pointer;
  background-color: transparent;
  color: rgb(156 163 175);
}

.dark .Header_darkModeButton__JU45P {
  color: rgb(229 231 235);
}

.Header_hamburgerMenuFiX__qJ9zf {
  color: rgb(75 85 99);
}

.Header_hamburgerMenuFiMenu__NS4ua {
  color: rgb(75 85 99);
}

.dark .Header_hamburgerMenuFiX__qJ9zf {
  color: white;
}

.dark .Header_hamburgerMenuFiMenu__NS4ua {
  color: white;
}

@media (min-width: 640px) {
  .Header_navClass__A860e {
    margin-left: 5.55555%;
    margin-right: 5.55555%;
  }

  .Header_containerHamburgerMenu___nWUB {
    gap: 10px;
  }
}

@media (min-width: 1024px) {
  .Header_darkModeButton__JU45P{
    margin-left: 2rem;
  }
  .Header_containerMenuItems__sRsw0 {
    display: flex;
  }
  .Header_containerButtons__LQhDH {
    display: block;
  }
  .Header_containerHamburgerMenu___nWUB {
    display: none;
  }
}

@media (min-width: 1280px) {
  .Header_navClass__A860e {
    margin-left: 11.1111%;
    margin-right: 11.1111%;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/HeaderMenuItem.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
.HeaderMenuItem_linkMenuItem__xF2gL {
  display: none;
  justify-content: center;
  align-items: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-radius: 5px;
  transition-property: color, background-color, border-color,
    fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  color: rgb(75 85 99);
}

.HeaderMenuItem_linkMenuItemActive__nSrsg {
  color: rgb(2 132 199);
  background-color: rgb(14 165 233 / 0.1);
}

.dark .HeaderMenuItem_linkMenuItemActive__nSrsg {
  color: white;
  background-color: rgb(14 165 233 / 0.3);
}

.dark .HeaderMenuItem_linkMenuItem__xF2gL {
  color: white;
}

.HeaderMenuItem_linkMenuItem__xF2gL:hover {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

.dark .HeaderMenuItem_linkMenuItem__xF2gL:hover {
  color: white;
  background-color: rgb(14 165 233 / 0.4);
}

.HeaderMenuItem_linkHamburgerMenuItem__3SS_n {
  display: block;
  font-size: 0.875rem;
  line-height: 1.25rem;
  margin-bottom: 0.75rem;
  transition-property: color, background-color, border-color,
    fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  color: rgb(31 41 55);
}

/*:global(.dark) .linkHamburgerMenuItem {
  color: white;
}*/

.HeaderMenuItem_linkHamburgerMenuItem__3SS_n:hover {
  color: rgb(2 132 199);
}

/*:global(.dark) .linkHamburgerMenuItem:hover {
  color: rgb(14 165 233);
}*/

.HeaderMenuItem_containerLinkItemWithSubItems__g_lMm {
  display: none;
  position: relative;
}

.HeaderMenuItem_buttonLinkItemWithSubItems__8HrFf {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 700;
  line-height: 1.25rem;

  color: rgb(75 85 99);
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-radius: 5px;
  transition-property: color, background-color, border-color,
    fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  background-color: transparent;
}

.dark .HeaderMenuItem_buttonLinkItemWithSubItems__8HrFf {
  color: white;
}

.HeaderMenuItem_buttonLinkItemWithSubItems__8HrFf:hover {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

.HeaderMenuItem_buttonLinkItemWithSubItems__8HrFf:focus {
  background-color: rgb(14 165 233 / 0.2);
  color: rgb(2 132 199);
}

.dark .HeaderMenuItem_buttonLinkItemWithSubItems__8HrFf:hover {
  color: white;
  background-color: rgb(14 165 233 / 0.4);
}
.dark .HeaderMenuItem_buttonLinkItemWithSubItems__8HrFf:focus {
  color: white;
  background-color: rgb(14 165 233 / 0.4);
}

.HeaderMenuItem_buttonLinkItemWithSubItemsOpen__wCX_a {
  background-color: rgb(14 165 233 / 0.4);
  color: rgb(2 132 199);
}

.dark .HeaderMenuItem_buttonLinkItemWithSubItemsOpen__wCX_a {
  color: white;
}

.HeaderMenuItem_buttonTextActive__uM2P_ {
  color: rgb(2 132 199);
  background-color: rgb(14 165 233 / 0.1);
}

.dark .HeaderMenuItem_buttonTextActive__uM2P_ {
  color: rgb(56 189 248);
}

.HeaderMenuItem_svgClass__bND1l {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-left: 5px;
}

.HeaderMenuItem_containerSubmenuItemsOpen__3twwp {
  position: absolute;
  top: 2.25rem;
  z-index: 10;
  width: 16rem;
  background-color: white;
  padding: 0.75rem;
  border-width: 1px;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/*:global(.dark) .containerSubmenuItemsOpen {
  background-color: rgb(17 24 39);
  border-color: rgb(156 163 175);
}*/

.HeaderMenuItem_containerSubmenuItems__XN0zv {
  margin-bottom: 1.5rem;
}

.HeaderMenuItem_containerLinkText__6PGhX {
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 800;
  text-transform: uppercase;
  color: rgb(107 114 128);
  letter-spacing: 0.35rem;
  margin-bottom: 1rem;
}

/*:global(.dark) .containerLinkText {
  color: white;
}*/

@media (min-width: 1024px) {
  .HeaderMenuItem_linkMenuItem__xF2gL {
    display: inline-flex;
  }
  .HeaderMenuItem_linkHamburgerMenuItem__3SS_n {
    display: none;
  }
  .HeaderMenuItem_containerLinkItemWithSubItems__g_lMm {
    display: block;
  }
  .HeaderMenuItem_containerSubmenuItems__XN0zv {
    display: none;
  }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./css/HeaderMenuSubItem.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
.HeaderMenuSubItem_linkContainer__akJQf {
  padding: 0;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: flex-start;
}

.HeaderMenuSubItem_fiContainer__CpmmY {
  color: rgb(14 165 233);
  margin-right: 0.5rem;
}

.HeaderMenuSubItem_textContainer__kVlwj {
  flex: 1 1;
  overflow: hidden;
}

.HeaderMenuSubItem_linkContainer__akJQf:hover .HeaderMenuSubItem_linkText__7r20H {
  color: rgb(2 132 199);
}

/*:global(.dark) .linkContainer:hover .linkText {
  color: rgb(14 165 233);
}*/

.HeaderMenuSubItem_linkText__7r20H {
  color: rgb(17 24 39);
  font-size: 0.875rem;
  line-height: 1.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition-property: color, background-color, border-color,
    fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/*:global(.dark) .linkText {
  color: white;
}*/

.HeaderMenuSubItem_descriptionContainer__9mE5_ {
  display: none;
}

.HeaderMenuSubItem_linkDescription__vvDtb {
  color: rgb(75 85 99);
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition-property: color, background-color, border-color,
    fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/*:global(.dark) .linkDescription {
  color: white;
}*/

@media (min-width: 1024px) {
  .HeaderMenuSubItem_linkContainer__akJQf {
    padding: 0.75rem;
  }
  .HeaderMenuSubItem_fiContainer__CpmmY {
    display: none;
  }
  .HeaderMenuSubItem_textContainer__kVlwj {
    overflow: auto;
  }
  .HeaderMenuSubItem_linkText__7r20H {
    overflow: auto;
    white-space: normal;
    font-weight: 700;
  }
  .HeaderMenuSubItem_descriptionContainer__9mE5_ {
    display: block;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./css/style.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
@tailwind base;
@tailwind components;
@tailwind utilities;

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

html {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  tab-size: 4;
  /* 3 */
}

body {
  margin: 0;
  line-height: inherit;
}

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b,
strong {
  font-weight: bolder;
}

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    'Liberation Mono', 'Courier New', monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

button,
select {
  text-transform: none;
}

button,
[type='button'],
[type='reset'],
[type='submit'] {
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type='search'] {
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

summary {
  display: list-item;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

button,
[role='button'] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/*
Ensure the default browser behavior of the `hidden` attribute.
*/

[hidden] {
  display: none;
}

/* Import Google Fonts for elegant typography */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: white;
  font-family: 'Inter', system-ui, sans-serif;
}

.whiteContentClass {
  background-color: white;
}

.darkContentClass {
  background-color: rgb(17 24 39);
}

.dark body {
  background-color: rgb(17 24 39);
}

/* Florist-specific utility classes */
.font-light {
  font-weight: 300;
}

.font-elegant {
  font-family: 'Playfair Display', Georgia, serif;
}

.text-shadow-soft {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bg-gradient-florist {
  background: linear-gradient(135deg, #f0f8f0 0%, #e3e7e3 100%);
}

.bg-gradient-sage {
  background: linear-gradient(135deg, #f6f7f6 0%, #e3e7e3 100%);
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for better aesthetics */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

